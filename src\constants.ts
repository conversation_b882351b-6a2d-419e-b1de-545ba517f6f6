export const DAYOFMONTH = 30;
export const MONTHS = 12;
export const LENGTH_CODE = 17;
export const PREFIX_TT = '43';

export enum LOG {
  LOGIN = 4,
  CREATE = 3,
  UPDATE = 5,
  DELETE = 6,
  VEHICLE_IN = 65,
  VEHICLE_OUT = 66,
  ATTENDANCELOG = 67,
}

export enum FeaturesDataType {
  BOOLEAN = 'boolean',
  DATE = 'date',
  NUMBER = 'number',
  RANGE = 'range',
  SELECTED = 'selected',
  TEXT = 'text',
  OBJECT_LIST = 'objectList',
  MULTI = 'multi',
  OBJECT = 'object',
  TABLE = 'table',
  OBJECT_LIST_RANGE = 'objectListRange',
  OBJECT_IN_RANGE = 'objectInRange',
}

export enum Status {
  NONEFFECTIVE = 0,
  EFFECTIVE = 1,
  EXPIRED = 2,
}

export enum LevelData {
  ALL = 'ALL',
  SELF = 'SELF',
  DIVISION = 'DIVISION',
  REGION = 'REGION',
}

export enum TypeScopeOrganization {
  ALL = 'ALL',
  ORG = 'ORG',
  VTD = 'VTD',
}

export enum RolePerms {
  systems = 'systems',

  users = 'users',
  viewUser = 'view_users',
  editUser = 'edit_users',

  roles = 'roles',
  editRole = 'edit_roles',
  viewRole = 'view_roles',

  permissions = 'permissions',
  viewPermission = 'view_permissions',
  editPermission = 'edit_permissions',

  logs = 'logs',
  viewLogs = 'view_logs',

  organizations = 'organizations',
  viewOrganizations = 'view_organizations',
  editOrganizations = 'edit_organizations',
  deleteOrganizations = 'delete_organizations',

  search = 'searchs',
  viewSearchVehicle = 'view_tim-kiem-phuong-tiens',
  viewSearchGuestLog = 'view_tim-kiem-khachs',

  reporting = 'reporting',

  userGuide = 'user-guides',
  editUserGuide = 'edit_user-guides',

  reportError = 'report-errors',
  editReportError = 'edit_report-errors',

  errors = 'errors',
  editError = 'edit_errors',

  dashboards = 'dashboards',
  viewDashboards = 'view_dashboards',

  files = 'files',
  viewFiles = 'view_files',
  editFiles = 'edit_files',

  situationSummary = 'tinh-hinh-ngays',
  editSituationSummary = 'edit_tinh-hinh-ngays',
  viewSituationSummary = 'view_tinh-hinh-ngays',

  shiftHandover = 'so-ca-gacs',
  viewShiftHandover = 'view_so-ca-gacs',
  editShiftHandover = 'edit_so-ca-gacs',

  equipment = 'dm-ttbs',
  viewEquipment = 'view_dm-ttbs',
  editEquipment = 'edit_dm-ttbs',

  workingSchedule = 'dm-gio-lam-viecs',
  viewWorkingSchedule = 'view_dm-gio-lam-viecs',
  editWorkingSchedule = 'edit_dm-gio-lam-viecs',

  guardPosts = 'vong-gacs',
  viewGuardPost = 'view_vong-gacs',
  editGuardPost = 'edit_vong-gacs',

  guestLog = 'vao-ra-khachs',
  viewGuestLog = 'view_vao-ra-khachs',
  editGuestLog = 'edit_vao-ra-khachs',

  businessGuest = 'khach-cong-tacs',
  viewBusinessGuest = 'view_khach-cong-tacs',
  editBusinessGuest = 'edit_khach-cong-tacs',

  viewQlLichTrucBanNoiVus = 'view_ql-lich-truc-ban-noi-vus',
  editQlLichTrucBanNoiVus = 'edit_ql-lich-truc-ban-noi-vus',

  viewLichTrucBans = 'view_lich-truc-bans',
  editLichTrucBans = 'edit_lich-truc-bans',

  shiftInspections = 'ktra-gacs',
  viewShiftInspection = 'view_ktra-gacs',
  editShiftInspection = 'edit_ktra-gacs',

  viewEquipHandover = 'view_ban-giao-ttbs',
  editEquipHandover = 'edit_ban-giao-ttbs',
  deleteEquipHandover = 'delete_ban-giao-ttbs',

  guardShift = 'lich-gacs',
  viewGuardShift = 'view_lich-gacs',
  editGuardShift = 'edit_lich-gacs',

  guardShiftManagement = 'ql-lich-gacs',
  viewGuardShiftManagement = 'view_ql-lich-gacs',
  editGuardShiftManagement = 'edit_ql-lich-gacs',

  configShift = 'dm-ca-gacs',
  viewConfigShift = 'view_dm-ca-gacs',
  editConfigShift = 'edit_dm-ca-gacs',

  eqn = 'cbnvs',
  viewEqn = 'view_cbnvs',
  editEqn = 'edit_cbnvs',

  vehicle = 'phuong-tiens',
  viewVehicle = 'view_phuong-tiens',
  editVehicle = 'edit_phuong-tiens',

  vehicleInOut = 'vao-ra-phuong-tiens',
  viewVehicleInOut = 'view_vao-ra-phuong-tiens',
  editVehicleInOut = 'edit_vao-ra-phuong-tiens',

  export = 'exports',
  viewExportGuestLog = 'view_trich-xuat-khachs',
  viewExportGuestVehicle = 'view_trich-xuat-phuong-tien-khachs',
  viewExportQNVehicle = 'view_trich-xuat-phuong-tien-qns',
  viewExportDetailDuty = 'view_danh-sach-truc-bans',
  viewExportSituationSummary = 'view_trich-xuat-tinh-hinh-ngays',
  viewExportTimeSheet = 'view_trich-xuat-cham-congs',

  timeSheetManagement = 'ql-cham-congs',
  viewTimeSheetSummary = 'view_tong-hop-thong-tin-cham-congs',
  viewTimeSheetManagement = 'view_ql-cham-cong-can-bo-nhan-viens',

  qnVehicleSummary = 'tong-hop-vao-ra-phuong-tien-quan-nhans',
  viewQnVehicleSummary = 'view_tong-hop-vao-ra-phuong-tien-quan-nhans',

  qnVehicleManagement = 'ql-phuong-tien-quan-nhans',
  viewQNVehicleManagement = 'view_ql-phuong-tien-quan-nhans',
  editQNVehicleManagement = 'edit_ql-phuong-tien-quan-nhans',

  inoutOrganizationSummary = 'tong-hop-vao-ra-phuong-tien-don-vis',
  viewInoutOrganizationSummary = 'view_tong-hop-vao-ra-phuong-tien-don-vis',

  inoutOrganizationManagement = 'ql-phuong-tien-don-vis',
  viewInoutOrganizationManagement = 'view_ql-phuong-tien-don-vis',
  editInoutOrganizationManagement = 'edit_ql-phuong-tien-don-vis',

  viewGuestInDuty = 'view_tong-hop-khach-vao-ra-trong-ca-truc-ban-noi-vus',
  viewLookupGuestInDuty = 'view_tra-cuu-tong-hop-khach-vao-ra-don-vis',
  viewGuestOrganizationInDuty = 'view_tong-hop-thong-tin-phuong-tien-vao-ra-don-vi-trong-ca-gacs',
  viewInoutVehicleHistory = 'view_quan-ly-lich-su-vao-ra-cau-phuong-tiens',
  viewSearchVehicleInOutOrganization = 'view_tong-hop-tim-kiem-thong-tin-phuong-tien-ra-vao-don-vis',
  viewVehicleSummary = 'view_tong-hop-phuong-tiens',
  viewGuestSummary = 'view_tong-hop-khachs',
  viewTongHopTHTrucBan = 'view_tong-hop-thtbs',
  viewTongHopChamCong = 'view_tong-hop-cham-congs',

  bookManagement = 'ql-sos',

  viewChamCong = 'view_cham-congs',
  editChamCong = 'edit_cham-congs',
}

export enum FileTypes {
  errors = 'errors',
}

export enum WorkingStatus {
  ENTERED = 15,
  GO_OUT = 16,
}

export enum InOutStatus {
  IN = 'in',
  OUT = 'out',
}

export enum TYPE_SCOPES {
  ENTRY_STATUSES = 'ENTRY_STATUSES',
  GUEST_TYPES = 'GUEST_TYPES',
  PURPOSE_CATEGORIES = 'PURPOSE_CATEGORIES',
  SEX_TYPES = 'SEX_TYPES',
  VEHICLE_TYPES = 'VEHICLE_TYPES',
  VEHICLE_CLASSES = 'VEHICLE_CLASSES',
  IDENTIFY_DOCUMENT_TYPES = 'IDENTIFY_DOCUMENT_TYPES',
  RANK = 'RANK',
  POSITION = 'POSITION',
  LOG = 'LOG',
}
