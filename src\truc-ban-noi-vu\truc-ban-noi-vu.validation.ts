import Joi from 'joi';
import { IApiError } from '../types/validation';
export const eSearch: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};

export const jIdInParams = Joi.object({
  id: Joi.string().required(),
});

export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

export const eCreate: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const eUpdate: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};
export const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export const jBodyCauHinheQNLoaiHinhTruc = Joi.object({
  eqn: Joi.string().required(),
});
