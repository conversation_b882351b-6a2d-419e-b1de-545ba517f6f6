import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createCauHinheQNLoaiHinhTruc } from './truc-ban-noi-vu.controller';
import { createValidator } from '../utils/validator';
import {
  eCreate,
  jBodyCauHinheQNLoaiHinhTruc,
} from './truc-ban-noi-vu.validation';
const router = express.Router();
router.post(
  '/',
  checkAuth,
  createValidator('body', jBodyCauHinheQNLoaiHinhTruc, eCreate),
  createCauHinheQNLoaiHinhTruc,
);
export default router;
