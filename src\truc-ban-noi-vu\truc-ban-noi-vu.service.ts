import { insertLog } from '../logs/logs.service';
import { database } from '../config';
import { CauHinhEqnLoaiHinhTruc, DMLoaiHinhTruc, eQN, User } from '../models';
import { ERROR } from '../utils/error';
import { LOG } from '../constants';

export const create = async (eqn: string, user: User) => {
  const repo = database.getRepository(CauHinhEqnLoaiHinhTruc);
  const repoEQN = database.getRepository(eQN);
  const loaiHinhTrucRepo = database.getRepository(DMLoaiHinhTruc);

  const exitstedEqn = await repoEQN.findOne({
    where: { id: eqn },
    withDeleted: true,
  });
  if (!exitstedEqn) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  const loaiHinhTrucs = await loaiHinhTrucRepo.find({
    withDeleted: true,
  });
  if (loaiHinhTrucs.length === 0) {
    throw new Error(ERROR.LOAI_HINH_TRUC_NOT_FOUND);
  }

  const cauHinhEqnLoaiHinhTrucs = await Promise.all(
    loaiHinhTrucs.map(async (loaiHinhTruc) => {
      let cauHinhEqnLoaiHinhTruc = await repo.findOne({
        where: { eqn: eqn, maLoaiHinhTruc: loaiHinhTruc.ma },
        withDeleted: true,
      });
      if (cauHinhEqnLoaiHinhTruc) {
        return cauHinhEqnLoaiHinhTruc;
      } else {
        cauHinhEqnLoaiHinhTruc = new CauHinhEqnLoaiHinhTruc();
        cauHinhEqnLoaiHinhTruc.eQN = exitstedEqn;
        cauHinhEqnLoaiHinhTruc.value = '1';
        cauHinhEqnLoaiHinhTruc.maLoaiHinhTruc = loaiHinhTruc.ma;
        cauHinhEqnLoaiHinhTruc.loaiHinhTruc = loaiHinhTruc;
      }
      const newCauHinhEqnLoaiHinhTruc = repo.create(cauHinhEqnLoaiHinhTruc);
      return repo.save(newCauHinhEqnLoaiHinhTruc);
    }),
  );

  await insertLog({
    ...(user && {
      content: `Tạo mới cấu hình quân nhân trong loại hình lịch trực`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),

    updatedAt: new Date(),
  });
  return { data: cauHinhEqnLoaiHinhTrucs };
};
