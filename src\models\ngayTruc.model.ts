import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import EntityModel from './entity.model';

@Entity()
export class NgayTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  ngay?: number;

  @Column()
  thang?: number;

  @Column()
  nam?: number;

  @Column()
  tongQuanSo?: number;

  @Column({ default: false })
  isTangCuong?: boolean;

  @Column({
    type: 'date',
  })
  date?: Date;
}
